@import "../../styles/variables";

.navigation {
  width: 100vw;
  padding: 20px 2rem;
  background-color: rgba(255, 255, 255, 0.95);
  position: sticky;
  top: 0;
  z-index: $z-index-navigation;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
  backdrop-filter: blur(10px);
}

.navMenu {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4rem;
}

// Hamburger button - hidden by default
.hamburger {
  display: none;
  flex-direction: column;
  justify-content: space-around;
  width: 30px;
  height: 30px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  z-index: $z-index-navigation + 1;

  &:focus {
    outline: 2px solid $color-primary;
    outline-offset: 2px;
  }
}

.hamburgerLine {
  width: 30px;
  height: 3px;
  background-color: $color-secondary;
  border-radius: 2px;
  transition: all $transition-normal;
  transform-origin: center;
}

// Hamburger animation when active
.hamburgerLineActive {
  &:nth-child(1) {
    transform: rotate(45deg) translate(7px, 7px);
  }

  &:nth-child(2) {
    opacity: 0;
  }

  &:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -7px);
  }
}

// Overlay for mobile menu
.overlay {
  display: none;
}

.navItem {
  font-family: "Playfair Display", serif;
  font-size: 34px;
  font-weight: 400;
  color: rgb(186, 149, 173);
  cursor: pointer;
  transition: all $transition-normal, font-size $transition-slow;
  position: relative;
  text-decoration: none;

  // Reset link styles for React Router Link components
  &:link,
  &:visited {
    color: rgb(186, 149, 173);
    text-decoration: none;
  }
}

.navItem:hover {
  color: rgb(220, 156, 198);
  transform: translateY(-2px);
}

.navItem::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, rgb(220, 156, 198), rgb(186, 149, 173));
  transition: width 0.3s ease;
}

.navItem:hover::after {
  width: 100%;
}

// Medium screens - reduce gap and font size
@media (max-width: 900px) {
  .navMenu {
    gap: $spacing-sm;
    transition: gap $transition-normal;
  }

  .navItem {
    font-size: $font-size-4xl;
    transition: all $transition-normal, font-size $transition-slow;

    &:hover {
      transform: translateY(-1px) scale(1.02);
    }
  }
}

@media (max-width: $breakpoint-tablet) {
  .navMenu {
    gap: $spacing-md;
    transition: gap $transition-normal;
  }

  .navItem {
    font-size: $font-size-5xl;
    transition: all $transition-normal, font-size $transition-slow;

    &:hover {
      transform: translateY(-1px) scale(1.02);
    }
  }
}

@media (max-width: $breakpoint-mobile) {
  .navigation {
    justify-content: space-between;
    padding: $spacing-sm 1rem;
  }

  // Show hamburger button on mobile
  .hamburger {
    display: flex;
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
  }

  // Hide navigation menu by default on mobile
  .navMenu {
    position: fixed;
    top: 0;
    right: -100%;
    width: 80%;
    height: 100vh;
    background-color: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(15px);
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: $spacing-lg;
    transition: right $transition-normal;
    z-index: $z-index-navigation;
    box-shadow: $shadow-lg;
  }

  // Show navigation menu when open
  .navMenuOpen {
    right: 0;
  }

  .navItem {
    font-size: $font-size-6xl;
    padding: $spacing-sm 0;
    width: 100%;
    text-align: center;
    color: $color-secondary;

    &:hover {
      color: $color-primary;
      transform: scale(1.05);
    }
  }

  // Show overlay when menu is open
  .overlay {
    display: block;
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: $z-index-navigation - 1;
    backdrop-filter: blur(2px);
  }
}
