@import "../../styles/variables";

.navigation {
  width: 100vw;
  padding: 20px 2rem;
  background-color: rgba(255, 255, 255, 0.95);
  position: sticky;
  top: 0;
  z-index: $z-index-navigation;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
  backdrop-filter: blur(10px);
}

.navMenu {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4rem;
}

// Hamburger button - hidden by default
.hamburger {
  display: none;
  flex-direction: column;
  justify-content: space-around;
  width: 30px;
  height: 30px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  z-index: $z-index-navigation + 1;

  &:focus {
    outline: 2px solid $color-primary;
    outline-offset: 2px;
  }
}

.hamburgerLine {
  width: 30px;
  height: 3px;
  background-color: $color-secondary;
  border-radius: 2px;
  transition: all $transition-normal;
  transform-origin: center;
}

// Hamburger animation when active
.hamburgerLineActive {
  &:nth-child(1) {
    transform: rotate(45deg) translate(7px, 7px);
  }

  &:nth-child(2) {
    opacity: 0;
  }

  &:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -7px);
  }
}

// Overlay for mobile menu
.overlay {
  display: none;
}

.navItem {
  font-family: "Playfair Display", serif;
  font-size: 34px;
  font-weight: 400;
  color: rgb(186, 149, 173);
  cursor: pointer;
  transition: all $transition-normal, font-size $transition-slow;
  position: relative;
  text-decoration: none;

  // Reset link styles for React Router Link components
  &:link,
  &:visited {
    color: rgb(186, 149, 173);
    text-decoration: none;
  }
}

.navItem:hover {
  color: rgb(220, 156, 198);
  transform: translateY(-2px);
}

.navItem::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, rgb(220, 156, 198), rgb(186, 149, 173));
  transition: width 0.3s ease;
}

.navItem:hover::after {
  width: 100%;
}

// Tablet and mobile - show hamburger menu
@media (max-width: $breakpoint-tablet) {
  .navigation {
    justify-content: space-between;
    padding: $spacing-sm 1rem;
  }

  // Show hamburger button on mobile
  .hamburger {
    display: flex;
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
  }

  // Hide navigation menu by default on mobile
  .navMenu {
    position: fixed;
    top: 0;
    right: -100%;
    width: 80%;
    height: 100vh;
    background-color: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(15px);
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: $spacing-lg;
    transition: right $transition-normal;
    z-index: $z-index-navigation;
    box-shadow: $shadow-lg;

    // Staggered animation for menu items
    .navItem {
      opacity: 0;
      transform: translateX(30px);
      transition: all $transition-normal;

      &:nth-child(1) {
        transition-delay: 0.1s;
      }
      &:nth-child(2) {
        transition-delay: 0.15s;
      }
      &:nth-child(3) {
        transition-delay: 0.2s;
      }
      &:nth-child(4) {
        transition-delay: 0.25s;
      }
      &:nth-child(5) {
        transition-delay: 0.3s;
      }
    }
  }

  // Show navigation menu when open
  .navMenuOpen {
    right: 0;

    .navItem {
      opacity: 1;
      transform: translateX(0);
    }
  }

  .navItem {
    font-size: $font-size-6xl;
    padding: $spacing-md $spacing-xl;
    width: auto;
    text-align: center;
    color: $color-secondary;
    position: relative;
    border-radius: $border-radius-lg;
    transition: all $transition-normal;

    // Elegant underline effect
    &::before {
      content: "";
      position: absolute;
      bottom: 8px;
      left: 50%;
      transform: translateX(-50%);
      width: 0;
      height: 2px;
      background: linear-gradient(90deg, $color-primary, $color-secondary);
      transition: width $transition-normal;
      border-radius: 1px;
    }

    // Subtle background on hover
    &::after {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(
        135deg,
        rgba($color-primary, 0.08),
        rgba($color-secondary, 0.05)
      );
      border-radius: $border-radius-lg;
      opacity: 0;
      transition: opacity $transition-normal;
      z-index: -1;
    }

    &:hover {
      color: $color-primary;
      transform: translateY(-2px) scale(1.02);

      &::before {
        width: 60%;
      }

      &::after {
        opacity: 1;
      }
    }

    // Active state animation
    &:active {
      transform: translateY(0) scale(0.98);
    }
  }

  // Show overlay when menu is open
  .overlay {
    display: block;
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: $z-index-navigation - 1;
    backdrop-filter: blur(2px);
  }
}
