@import '../../styles/_variables.scss';

.account {
  padding: $spacing-lg;
  max-width: 600px;
  margin: $spacing-2xl auto;
  background: $color-bg-white;
  border-radius: $border-radius-md;
  box-shadow: $shadow-sm;

  h1 {
    font-family: $font-primary;
    font-size: $font-size-5xl;
    margin-bottom: $spacing-md;
    color: $color-text-primary;
  }

  p {
    margin-bottom: $spacing-sm;
    color: $color-text-secondary;
    font-family: $font-secondary;

    strong {
      color: $color-text-dark;
    }

    code {
      display: block;
      margin-top: $spacing-xs;
      padding: $spacing-xs $spacing-sm;
      background: $color-bg-input;
      border-radius: $border-radius-sm;
      font-family: $font-secondary;
      font-size: $font-size-sm;
      word-break: break-all;
    }
  }

  .logoutBtn {
    margin-top: $spacing-lg;
    padding: $spacing-sm $spacing-md;
    background: $color-danger;
    color: $color-bg-white;
    border: none;
    border-radius: $border-radius-sm;
    font-family: $font-secondary;
    font-weight: $font-weight-semibold;
    cursor: pointer;
    transition: background $transition-fast;

    &:hover {
      background: darken($color-danger, 10%);
    }
  }
}
