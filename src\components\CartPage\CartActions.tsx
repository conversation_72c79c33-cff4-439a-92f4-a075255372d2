import type { Notification } from '../Notifications/Notification';
import type { CartItem } from './CartItem';

interface CartProps {
    setNotifications: React.Dispatch<React.SetStateAction<Notification[]>>;
    notifications: Notification[];
}

const UseCart = ({ setNotifications, notifications }: CartProps) => {
    const addItemToCart = (item: CartItem) => {
        const cart: CartItem[] = JSON.parse(localStorage.getItem('cart') || '[]');
        const index = cart.findIndex(cartItem => cartItem.name === item.name);
        if (index !== -1) {
            // Increment quantity if item already in cart
            cart[index].quantity = (cart[index].quantity || 1) + 1;
        } else {
            cart.push({ ...item, quantity: 1 });
        }
        // Save updated cart
        localStorage.setItem('cart', JSON.stringify(cart));
        setNotifications([...notifications, { id: Date.now(), message: `"${item.name}" has been added to your cart` }]);
    };

    const removeItemFromCart = (item: CartItem) => {
        const cart: CartItem[] = JSON.parse(localStorage.getItem('cart') || '[]');
        const updatedCart = cart.filter(cartItem => cartItem.name !== item.name);
        localStorage.setItem('cart', JSON.stringify(updatedCart));
        setNotifications([...notifications, { id: Date.now(), message: `"${item.name}" has been removed from your cart` }]);
    };

    const changeItemQuantity = (item: CartItem, quantity: number) => {
        const cart: CartItem[] = JSON.parse(localStorage.getItem('cart') || '[]');
        const updatedCart = cart.map(cartItem => {
            if (cartItem.name === item.name) {
                return { ...cartItem, quantity };
            }
            return cartItem;
        });
        localStorage.setItem('cart', JSON.stringify(updatedCart));
    };

    const clearCart = () => {
        localStorage.setItem('cart', JSON.stringify([]));
        setNotifications([...notifications, { id: Date.now(), message: 'Cart has been cleared' }]);
    };

    const getCartItems = () => {
        return JSON.parse(localStorage.getItem('cart') || '[]');
    };

    return {
        addItemToCart,
        removeItemFromCart,
        changeItemQuantity,
        clearCart,
        getCartItems
    };
};

export default UseCart;