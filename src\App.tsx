import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import HomePage from './components/HomePage/HomePage';
import AboutUs from './components/AboutUs';
import ShopPage from './components/ShopPage/ShopPage';
import IngredientsPage from './components/IngredientsPage/IngredientsPage';
import FaqPage from './components/FaqPage/FaqPage';
import FloatingBubble from './components/FloatingBubble/FloatingBubble';
import CartPage from './components/CartPage/CartPage';
import Login from './components/Login/Login';
import './App.css';
import Notifications from './components/Notifications/Notifications';
import React from 'react';
import type { Notification } from './components/Notifications/Notification';
import Register from './components/Register/Register';
import AccountPage from './components/Account/AccountPage';
import CheckoutPage from './components/CheckoutPage/CheckoutPage';

const App = () => {
  const [notifications, setNotifications] = React.useState<Notification[]>([]);
  const [isLoggedIn, setIsLoggedIn] = React.useState<boolean>(false);

  return (
    <Router>
      <div className="App">
        <Routes>
          <Route path="/" element={<HomePage setNotifications={setNotifications} notifications={notifications} />} />
          <Route path="/shop" element={<ShopPage setNotifications={setNotifications} notifications={notifications} />} />
          <Route path="/ingredients" element={<IngredientsPage />} />
          <Route path="/about-us" element={<AboutUs />} />
          <Route path="/faq" element={<FaqPage />} />
          <Route path="/cart" element={<CartPage setNotifications={setNotifications} notifications={notifications} />} />
          <Route path="/account" element={<AccountPage setNotifications={setNotifications} notifications={notifications} setIsLoggedIn={setIsLoggedIn} isLoggedIn={isLoggedIn} />} />
          <Route path="/login" element={<Login setNotifications={setNotifications} notifications={notifications} setIsLoggedIn={setIsLoggedIn} isLoggedIn={isLoggedIn} />} />
          <Route path="/register" element={<Register setNotifications={setNotifications} notifications={notifications} setIsLoggedIn={setIsLoggedIn} isLoggedIn={isLoggedIn} />} />
          <Route path="/checkout" element={<CheckoutPage setNotifications={setNotifications} notifications={notifications} />} />
        </Routes>
        <FloatingBubble setNotifications={setNotifications} notifications={notifications} setIsLoggedIn={setIsLoggedIn} isLoggedIn={isLoggedIn} />
        <Notifications setNotifications={setNotifications} notifications={notifications} />
      </div>
    </Router>
  );
}

export default App;
