import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import styles from "./Navigation.module.scss";
import Divider from "../Divider/Divider";

const Navigation: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
  };

  return (
    <>
      <Divider />
      <nav className={styles.navigation}>
        {/* Hamburger button - visible only on mobile */}
        <button
          className={styles.hamburger}
          onClick={toggleMenu}
          aria-label="Toggle navigation menu"
          aria-expanded={isMenuOpen}
        >
          <span
            className={`${styles.hamburgerLine} ${
              isMenuOpen ? styles.hamburgerLineActive : ""
            }`}
          ></span>
          <span
            className={`${styles.hamburgerLine} ${
              isMenuOpen ? styles.hamburgerLineActive : ""
            }`}
          ></span>
          <span
            className={`${styles.hamburgerLine} ${
              isMenuOpen ? styles.hamburgerLineActive : ""
            }`}
          ></span>
        </button>

        {/* Navigation menu */}
        <div
          className={`${styles.navMenu} ${
            isMenuOpen ? styles.navMenuOpen : ""
          }`}
        >
          <Link to="/" className={styles.navItem} onClick={closeMenu}>
            HOME
          </Link>
          <Link to="/shop" className={styles.navItem} onClick={closeMenu}>
            SHOP
          </Link>
          <Link to="/about-us" className={styles.navItem} onClick={closeMenu}>
            ABOUT US
          </Link>
          <Link
            to="/ingredients"
            className={styles.navItem}
            onClick={closeMenu}
          >
            INGREDIENTS
          </Link>
          <Link to="/faq" className={styles.navItem} onClick={closeMenu}>
            FAQ
          </Link>
        </div>

        {/* Overlay for mobile menu */}
        {isMenuOpen && (
          <div className={styles.overlay} onClick={closeMenu}></div>
        )}
      </nav>
    </>
  );
};

export default Navigation;
