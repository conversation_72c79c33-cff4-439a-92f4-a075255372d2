@import "../../styles/_variables.scss";

.heroSection {
  width: 100%;
  padding: $spacing-4xl 2rem;
  text-align: center;
  background-color: $color-bg-white;

  .pageTitle {
    font-family: $font-primary;
    font-size: $font-size-hero-md;
    font-weight: $font-weight-extra-bold;
    color: $color-secondary;
    margin: 0;
  }
}

.checkout {
  width: 100vw;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: $color-bg-light;
  padding-top: $spacing-4xl;

  h1 {
    font-family: $font-primary;
    font-size: $font-size-hero-md;
    font-weight: $font-weight-extra-bold;
    color: $color-secondary;
    margin: 0 0 $spacing-4xl 0;
    text-align: center;
  }

  .container {
    max-width: 1366px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: $spacing-3xl;
    padding: 0 2rem;
    align-items: start;
    width: 100%;
    box-sizing: border-box;
  }

  section {
    background: $color-bg-white;
    border-radius: $border-radius-5xl;
    box-shadow: $shadow-sm;
    padding: $spacing-2xl;
  }

  .orderSummary {
    height: fit-content;
    align-self: start;

    h2 {
      margin-bottom: $spacing-lg;
      color: $color-text-primary;
      font-family: $font-primary;
      font-size: $font-size-3xl;
    }

    .itemsHeader {
      display: grid;
      grid-template-columns: 1fr 2fr 1fr 1fr;
      gap: $spacing-md;
      align-items: center;
      padding: $spacing-md 0;
      transition: $transition-fast;
      border-bottom: 1px solid rgba($color-border-light, 0.3);

      .productColumn {
        text-align: left;
        padding-left: $spacing-md;
      }

      .priceColumn,
      .qtyColumn,
      .totalColumn {
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }

    .itemsList {
      display: flex;
      flex-direction: column;
      gap: $spacing-sm;
      margin: 0 0 $spacing-lg 0;
      padding: 0;

      .item {
        display: grid;
        grid-template-columns: 1fr 2fr 1fr 1fr;
        gap: $spacing-md;
        align-items: center;
        padding: $spacing-md 0;
        transition: $transition-fast;
        border-bottom: 1px solid rgba($color-border-light, 0.3);

        &:last-child {
          border-bottom: none;
        }

        &:hover {
          background-color: rgba($color-bg-light, 0.2);
        }

        .itemImage {
          width: 80px;
          height: 80px;
          border-radius: $border-radius-lg;
          object-fit: cover;
          transition: $transition-fast;

          &:hover {
            transform: scale(0.99);
          }

          &:active {
            transform: scale(0.95);
          }
        }

        .productInfo {
          display: flex;
          align-items: center;
          gap: $spacing-sm;
          padding-left: $spacing-md;

          .itemName {
            font-family: $font-secondary;
            font-size: $font-size-base;
            font-weight: $font-weight-medium;
            color: $color-text-primary;
          }
        }

        .priceInfo,
        .quantityInfo,
        .totalInfo {
          display: flex;
          justify-content: center;
          align-items: center;
          text-align: center;
        }

        .itemPrice,
        .itemQuantity,
        .itemTotal {
          font-family: $font-secondary;
          font-size: $font-size-base;
          color: $color-text-primary;
        }

        .itemTotal {
          font-weight: $font-weight-semibold;
          color: $color-secondary;
        }
      }
    }

    .total {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: $font-weight-bold;
      font-size: $font-size-lg;
      margin-top: $spacing-lg;
      padding: $spacing-md 0;
      border-top: 2px solid $color-secondary;
      font-family: $font-primary;
      color: $color-text-primary;
    }
  }

  .shippingForm {
    h2 {
      margin-bottom: $spacing-md;
      color: $color-text-primary;
      font-family: $font-primary;
      font-size: $font-size-3xl;
    }

    .form {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: $spacing-md $spacing-lg;

      label {
        margin-bottom: $spacing-xs;
        font-weight: $font-weight-medium;
        color: $color-text-primary;
        font-family: $font-secondary;
        display: flex;
        align-items: center;
        gap: $spacing-xs;

        .required {
          color: $color-danger;
          font-weight: $font-weight-bold;
          font-size: $font-size-lg;
        }
      }

      input {
        margin-bottom: $spacing-md;
        padding: $spacing-sm $spacing-md;
        border: 1px solid $color-border-light;
        border-radius: $border-radius-md;
        font-family: $font-secondary;
        font-size: $font-size-base;
        transition: $transition-fast;
        background-color: $color-bg-white;
        min-height: 44px; // Touch-friendly minimum height
        width: 100%;
        box-sizing: border-box;

        &:focus {
          outline: none;
          border-color: $color-border-focus;
          box-shadow: 0 0 0 2px rgba(219, 152, 196, 0.2);
        }

        &:hover {
          border-color: $color-border-focus;
        }

        &:required {
          border-left: 3px solid $color-danger;
        }

        &:required:valid {
          border-left: 3px solid $color-primary;
        }

        // Better mobile input styling
        @media (max-width: $breakpoint-tablet) {
          padding: $spacing-md;
          font-size: 16px; // Prevents zoom on iOS
          border-radius: $border-radius-lg;
        }
      }

      // Field groups for individual columns
      .fieldGroup {
        display: flex;
        flex-direction: column;

        label {
          margin-bottom: $spacing-xs;
        }

        input {
          margin-bottom: 0;
        }
      }

      // Full width fields
      .fullWidth {
        grid-column: 1 / -1;
        display: flex;
        flex-direction: column;

        label {
          margin-bottom: $spacing-xs;
        }

        input {
          margin-bottom: 0;
        }
      }

      .requiredNote {
        display: flex;
        align-items: center;
        gap: $spacing-xs;
        margin: $spacing-md 0;
        font-family: $font-secondary;
        font-size: $font-size-sm;
        color: $color-text-muted;
        font-style: italic;
        grid-column: 1 / -1;

        .required {
          color: $color-danger;
          font-weight: $font-weight-bold;
          font-size: $font-size-base;
          font-style: normal;
        }
      }

      .submitBtn {
        margin-top: $spacing-md;
        padding: $spacing-md $spacing-lg;
        background: $color-bg-button-secondary;
        color: $color-text-primary;
        border: none;
        border-radius: $border-radius-5xl;
        cursor: pointer;
        font-family: $font-primary;
        font-size: $font-size-lg;
        font-weight: $font-weight-bold;
        transition: $transition-fast;
        width: 100%;
        grid-column: 1 / -1;

        &:hover {
          background: $color-primary-hover;
          transform: scale(0.99);
        }

        &:active {
          transform: scale(0.95);
        }
      }
    }
  }

  // Responsive Design

  // Large screens (wide desktop)
  @media (min-width: $breakpoint-wide) {
    .container {
      max-width: 1400px;
      gap: $spacing-4xl;
    }

    .shippingForm .form {
      gap: $spacing-lg $spacing-2xl;
    }
  }

  // Desktop
  @media (max-width: $breakpoint-desktop) {
    .container {
      gap: $spacing-2xl;
      padding: 0 $spacing-md;
    }

    .shippingForm .form {
      gap: $spacing-md $spacing-lg;
    }
  }

  // Large tablet (landscape)
  @media (max-width: 1024px) {
    .container {
      grid-template-columns: 1fr;
      gap: $spacing-xl;
      max-width: 800px;
    }

    .shippingForm {
      order: 1;

      .form {
        grid-template-columns: 1fr 1fr;
        gap: $spacing-md $spacing-lg;
      }
    }

    .orderSummary {
      order: 2;

      .itemsHeader {
        grid-template-columns: 2fr 1fr 1fr 1fr;
        font-size: $font-size-sm;
      }

      .itemsList .item {
        grid-template-columns: 2fr 1fr 1fr 1fr;

        .productInfo .itemName {
          font-size: $font-size-sm;
        }
      }
    }
  }

  // Tablet (portrait)
  @media (max-width: $breakpoint-tablet) {
    padding-top: $spacing-2xl;

    .heroSection {
      padding: $spacing-3xl 1.5rem;

      .pageTitle {
        font-size: $font-size-hero-md;
      }
    }

    .container {
      grid-template-columns: 1fr;
      gap: $spacing-2xl;
      padding: 0 1.5rem;
      max-width: 600px;
    }

    .shippingForm {
      .form {
        grid-template-columns: 1fr;
        gap: $spacing-md;

        .fieldGroup,
        .fullWidth {
          margin-bottom: $spacing-sm;
        }
      }
    }

    .orderSummary {
      .itemsHeader {
        display: none; // Hide header on tablet portrait
      }

      .itemsList {
        .item {
          grid-template-columns: 1fr;
          gap: $spacing-xs;
          padding: $spacing-md;
          border-radius: $border-radius-md;
          background-color: rgba($color-bg-light, 0.3);
          margin-bottom: $spacing-sm;

          .productInfo {
            justify-content: flex-start;
            padding-bottom: $spacing-sm;
            border-bottom: 1px solid $color-border-light;
            margin-bottom: $spacing-sm;

            .itemImage {
              width: 50px;
              height: 50px;
            }

            .itemName {
              font-size: $font-size-base;
              font-weight: $font-weight-semibold;
            }
          }

          .priceInfo,
          .quantityInfo,
          .totalInfo {
            justify-content: space-between;
            padding: $spacing-xs 0;
            border-bottom: 1px solid rgba($color-border-light, 0.3);

            &:last-child {
              border-bottom: none;
            }

            &::before {
              font-family: $font-secondary;
              font-size: $font-size-sm;
              font-weight: $font-weight-medium;
              color: $color-text-muted;
            }
          }

          .priceInfo::before {
            content: "Price:";
          }

          .quantityInfo::before {
            content: "Quantity:";
          }

          .totalInfo::before {
            content: "Total:";
          }

          .totalInfo {
            font-weight: $font-weight-bold;
            color: $color-secondary;
            border-top: 2px solid $color-secondary;
            padding-top: $spacing-sm;
            margin-top: $spacing-xs;
          }
        }
      }
    }
  }

  // Mobile devices
  @media (max-width: $breakpoint-mobile) {
    padding-top: $spacing-lg;

    .heroSection {
      padding: $spacing-2xl 1rem;

      .pageTitle {
        font-size: $font-size-hero-sm;
        line-height: 1.2;
      }
    }

    .container {
      padding: 0 1rem;
      gap: $spacing-xl;
      max-width: 100%;
    }

    section {
      padding: $spacing-lg;
      border-radius: $border-radius-lg;
    }

    .shippingForm {
      h2 {
        font-size: $font-size-xl;
        margin-bottom: $spacing-lg;
        text-align: center;
      }

      .form {
        grid-template-columns: 1fr;
        gap: $spacing-md;

        .fieldGroup,
        .fullWidth {
          margin-bottom: $spacing-md;

          label {
            font-size: $font-size-sm;
            margin-bottom: $spacing-xs;
          }

          input {
            padding: $spacing-md;
            font-size: $font-size-base;
            border-radius: $border-radius-lg;

            &:focus {
              box-shadow: 0 0 0 3px rgba(219, 152, 196, 0.3);
            }
          }
        }

        .requiredNote {
          text-align: center;
          font-size: $font-size-xs;
          margin: $spacing-lg 0;
        }

        .submitBtn {
          padding: $spacing-lg $spacing-xl;
          font-size: $font-size-lg;
          font-weight: $font-weight-bold;
          margin-top: $spacing-lg;
          border-radius: $border-radius-5xl;

          &:hover {
            transform: scale(0.98);
          }

          &:active {
            transform: scale(0.96);
          }
        }
      }
    }

    .orderSummary {
      h2 {
        font-size: $font-size-xl;
        margin-bottom: $spacing-lg;
        text-align: center;
      }

      .itemsList {
        .item {
          padding: $spacing-lg;
          margin-bottom: $spacing-md;
          border-radius: $border-radius-lg;
          box-shadow: $shadow-sm;

          .productInfo {
            padding-bottom: $spacing-md;
            margin-bottom: $spacing-md;

            .itemImage {
              width: 60px;
              height: 60px;
              border-radius: $border-radius-lg;
            }

            .itemName {
              font-size: $font-size-lg;
              font-weight: $font-weight-bold;
              line-height: 1.3;
            }
          }

          .priceInfo,
          .quantityInfo,
          .totalInfo {
            padding: $spacing-sm 0;
            font-size: $font-size-base;

            &::before {
              font-size: $font-size-sm;
              font-weight: $font-weight-semibold;
            }
          }

          .totalInfo {
            font-size: $font-size-lg;
            font-weight: $font-weight-bold;
            padding-top: $spacing-md;
            margin-top: $spacing-sm;
          }
        }
      }
    }
  }

  // Extra small devices
  @media (max-width: 360px) {
    .container {
      padding: 0 0.75rem;
    }

    section {
      padding: $spacing-md;
    }

    .heroSection {
      padding: $spacing-xl 0.75rem;

      .pageTitle {
        font-size: $font-size-2xl;
      }
    }

    .shippingForm .form {
      .fieldGroup,
      .fullWidth {
        input {
          padding: $spacing-sm $spacing-md;
          font-size: $font-size-sm;
        }
      }

      .submitBtn {
        padding: $spacing-md $spacing-lg;
        font-size: $font-size-base;
      }
    }
  }

  // Landscape orientation on mobile
  @media (max-width: $breakpoint-tablet) and (orientation: landscape) {
    .heroSection {
      padding: $spacing-lg 1.5rem;

      .pageTitle {
        font-size: $font-size-2xl;
      }
    }

    .container {
      gap: $spacing-lg;
    }

    section {
      padding: $spacing-md;
    }
  }

  // High DPI displays
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .orderSummary .itemsList .item .productInfo .itemImage {
      image-rendering: -webkit-optimize-contrast;
      image-rendering: crisp-edges;
    }
  }

  // Reduced motion preference
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }

    .submitBtn {
      &:hover,
      &:active {
        transform: none;
      }
    }
  }

  // Print styles
  @media print {
    .heroSection,
    .submitBtn {
      display: none;
    }

    .container {
      grid-template-columns: 1fr;
      gap: $spacing-lg;
      max-width: 100%;
      padding: 0;
    }

    section {
      box-shadow: none;
      border: 1px solid #000;
      page-break-inside: avoid;
    }

    .orderSummary {
      page-break-before: always;
    }
  }
}
