import React, { useState } from "react";
import styles from "./CheckoutPage.module.scss";
import type { NotificationsProps } from "../Notifications/Notification";
import placeholder from "../../assets/placeholder.jfif";
import type { CartItem } from "../CartPage/CartItem";
import UseCart from "../CartPage/CartActions";
import Navigation from "../Navigation";
import Footer from "../Footer";

const CheckoutPage: React.FC<NotificationsProps> = ({
  notifications,
  setNotifications,
}) => {
  // get cart items and actions
  const { getCartItems, clearCart } = UseCart({
    notifications,
    setNotifications,
  });
  const cartItems: CartItem[] = getCartItems();
  const total = cartItems.reduce(
    (sum, item) => sum + item.price * item.quantity,
    0
  );

  // shipping form state
  const [shippingInfo, setShippingInfo] = useState({
    firstName: "",
    lastName: "",
    company: "",
    country: "",
    city: "",
    address: "",
    zip: "",
    phone: "",
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setShippingInfo((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // normally send order to API
    setNotifications([
      ...notifications,
      { id: Date.now(), message: "Order placed successfully!" },
    ]);
    clearCart();
  };

  return (
    <>
      <Navigation />

      <section className={styles.heroSection}>
        <h1 className={styles.pageTitle}>Checkout</h1>
      </section>

      <div className={styles.checkout}>
        <div className={styles.container}>
          <section className={styles.shippingForm}>
            <h2>Shipping Information</h2>
            <form onSubmit={handleSubmit} className={styles.form}>
              <div className={styles.fieldGroup}>
                <label htmlFor="firstName">
                  First name: <span className={styles.required}>*</span>
                </label>
                <input
                  id="firstName"
                  name="firstName"
                  value={shippingInfo.firstName}
                  onChange={handleChange}
                  required
                />
              </div>

              <div className={styles.fieldGroup}>
                <label htmlFor="lastName">
                  Last name: <span className={styles.required}>*</span>
                </label>
                <input
                  id="lastName"
                  name="lastName"
                  value={shippingInfo.lastName}
                  onChange={handleChange}
                  required
                />
              </div>

              <div className={styles.fullWidth}>
                <label htmlFor="company">Company:</label>
                <input
                  id="company"
                  name="company"
                  value={shippingInfo.company}
                  onChange={handleChange}
                />
              </div>

              <div className={styles.fieldGroup}>
                <label htmlFor="country">
                  Country: <span className={styles.required}>*</span>
                </label>
                <input
                  id="country"
                  name="country"
                  value={shippingInfo.country}
                  onChange={handleChange}
                  required
                />
              </div>

              <div className={styles.fieldGroup}>
                <label htmlFor="city">
                  City: <span className={styles.required}>*</span>
                </label>
                <input
                  id="city"
                  name="city"
                  value={shippingInfo.city}
                  onChange={handleChange}
                  required
                />
              </div>

              <div className={styles.fullWidth}>
                <label htmlFor="address">
                  Address: <span className={styles.required}>*</span>
                </label>
                <input
                  id="address"
                  name="address"
                  value={shippingInfo.address}
                  onChange={handleChange}
                  required
                />
              </div>

              <div className={styles.fieldGroup}>
                <label htmlFor="zip">
                  Zip / postal code: <span className={styles.required}>*</span>
                </label>
                <input
                  id="zip"
                  name="zip"
                  value={shippingInfo.zip}
                  onChange={handleChange}
                  required
                />
              </div>

              <div className={styles.fieldGroup}>
                <label htmlFor="phone">
                  Phone number: <span className={styles.required}>*</span>
                </label>
                <input
                  id="phone"
                  name="phone"
                  type="tel"
                  value={shippingInfo.phone}
                  onChange={handleChange}
                  required
                />
              </div>

              <div className={styles.requiredNote}>
                <span className={styles.required}>*</span> Required fields
              </div>

              <button type="submit" className={styles.submitBtn}>
                Place Order
              </button>
            </form>
          </section>

          <section className={styles.orderSummary}>
            <h2>Your Order</h2>
            {cartItems.length === 0 ? (
              <p>Your cart is empty.</p>
            ) : (
              <>
                <div className={styles.itemsHeader}>
                  <div className={styles.productColumn}>Product</div>
                  <div className={styles.priceColumn}>Price</div>
                  <div className={styles.qtyColumn}>Qty.</div>
                  <div className={styles.totalColumn}>Total</div>
                </div>
                <div className={styles.itemsList}>
                  {cartItems.map((item) => (
                    <div key={item.id} className={styles.item}>
                      <img
                        src={item.image ?? placeholder}
                        alt={item.name}
                        className={styles.itemImage}
                      />
                      <div className={styles.productInfo}>
                        <span className={styles.itemName}>{item.name}</span>
                      </div>
                      <div className={styles.quantityInfo}>
                        <span className={styles.itemQuantity}>
                          {item.quantity}
                        </span>
                      </div>
                      <div className={styles.totalInfo}>
                        <span className={styles.itemTotal}>
                          ${(item.price * item.quantity).toFixed(2)}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </>
            )}
            <div className={styles.total}>
              <span>Total:</span>
              <span>${total.toFixed(2)}</span>
            </div>
          </section>
        </div>
      </div>
      <Footer />
    </>
  );
};

export default CheckoutPage;
