import React from "react";
import Navigation from "../Navigation";
import Footer from "../Footer";
import styles from "./CartPage.module.scss";
import placeholder from "../../assets/placeholder.jfif";
import UseCart from "./CartActions";
import type { NotificationsProps } from "../Notifications/Notification";
import type { CartItem } from "./CartItem";
import { FiTrash } from "react-icons/fi";
import { useNavigate } from "react-router-dom";

interface CartPageProps extends NotificationsProps {}

const CartPage: React.FC<CartPageProps> = ({
  setNotifications,
  notifications,
}) => {
  const [cartItems, setCartItems] = React.useState<CartItem[]>([]);
  const { getCartItems, removeItemFromCart, changeItemQuantity } = UseCart({
    setNotifications,
    notifications,
  });
  const navigate = useNavigate();

  React.useEffect(() => {
    const items = getCartItems();
    setCartItems(items);
  }, []);

  const subtotal = cartItems.reduce(
    (sum, item) => sum + item.price * item.quantity,
    0
  );
  const shipping = subtotal > 50 ? 0 : 5;
  const total = subtotal + shipping;

  return (
    <div className={styles.cartPage}>
      <Navigation />

      <section className={styles.heroSection}>
        <h1 className={styles.pageTitle}>Cart</h1>
      </section>

      <main className={styles.mainContent}>
        <div className={styles.container}>
          <section className={styles.cartSection}>
            {cartItems.length === 0 ? (
              <div className={styles.emptyMessage}>Your cart is empty.</div>
            ) : (
              <>
                <div className={styles.cartHeader}>
                  <div className={styles.productColumn}>Product</div>
                  <div className={styles.priceColumn}>Price</div>
                  <div className={styles.qtyColumn}>Qty.</div>
                  <div className={styles.totalColumn}>Total</div>
                  <div className={styles.actionColumn}></div>
                </div>
                {cartItems.map((item) => (
                  <div key={item.id} className={styles.cartItem}>
                    <div className={styles.productInfo}>
                      <div className={styles.imageContainer}>
                        <img
                          src={item.image ?? placeholder}
                          alt={item.name}
                          className={styles.itemImage}
                        />
                      </div>
                      <div className={styles.productDetails}>
                        <h3 className={styles.itemTitle}>{item.name}</h3>
                      </div>
                    </div>
                    <div className={styles.priceInfo}>
                      <span className={styles.itemPrice}>
                        ${item.price.toFixed(2)}
                      </span>
                    </div>
                    <div className={styles.quantityInfo}>
                      <input
                        type="number"
                        id={`qty-${item.id}`}
                        value={item.quantity}
                        onChange={(e) => {
                          changeItemQuantity(
                            item,
                            Math.max(1, Number(e.target.value))
                          );
                          setCartItems(getCartItems());
                        }}
                        min={1}
                        className={styles.quantityInput}
                      />
                    </div>
                    <div className={styles.totalInfo}>
                      <span className={styles.itemTotal}>
                        ${(item.price * item.quantity).toFixed(2)}
                      </span>
                    </div>
                    <div className={styles.actionInfo}>
                      <button
                        type="button"
                        className={styles.removeBtn}
                        onClick={() => {
                          removeItemFromCart(item);
                          setCartItems(getCartItems());
                        }}
                      >
                        <FiTrash size={18} />
                      </button>
                    </div>
                  </div>
                ))}
              </>
            )}
          </section>

          <section className={styles.orderSummary}>
            <h2 className={styles.summaryTitle}>Order Summary</h2>
            <div className={styles.summaryRow}>
              <span>Subtotal</span>
              <span>${subtotal.toFixed(2)}</span>
            </div>
            <div className={styles.summaryRow}>
              <span>Shipping</span>
              <span>${shipping.toFixed(2)}</span>
            </div>
            <div className={styles.summaryRow}>
              <strong>Total</strong>
              <strong>${total.toFixed(2)}</strong>
            </div>
            <button
              className={styles.checkoutBtn}
              onClick={() => navigate("/checkout")}
            >
              Checkout
            </button>
          </section>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default CartPage;
